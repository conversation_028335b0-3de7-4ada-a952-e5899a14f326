import { useEffect } from 'react';
import { getEnv } from '@/common/utils/getEnv';

export default ({ src, color, width, height }: any) => {
  // const host =
  //   process.env.NODE_ENV == 'development' ? '' : getEnv().ENV == 'prod' ? 'exchange/' : 'scenic/exchange/';
  useEffect(() => {
    const getEle = (id: any) => document.getElementById(id);
    const setSvg = () => {
      document.body.insertAdjacentHTML(
        'beforeend',
        `<svg
          id="svg"
          aria-hidden="true"
          style="position: absolute; width: 0px; height: 0px; overflow: hidden"
        ></svg>`,
      );
      return getEle('svg');
    };
    const svg: any = getEle('svg') || setSvg();
    if (!getEle(src)) {
      // 使用绝对路径确保在任何路由下都能正确访问
      fetch(`/tab_icon/${src}.svg`)
        .then((res) => {
          if (!res.ok) {
            console.warn(`Failed to load SVG: /tab_icon/${src}.svg`);
            return;
          }
          res.text().then((data) => {
            try {
              // 检查数据是否有效
              if (!data || data.indexOf('<svg') === -1 || data.indexOf('</svg>') === -1) {
                console.warn(`Invalid SVG data for: /tab_icon/${src}.svg`);
                return;
              }

              // // 单色图标
              // data = data.replace(/fill="#\w+"/g, 'fill="currentColor"');
              // // 多色图标
              // data = data.replace(/fill="#\w+"/g, (match) => {
              //   // 获取匹配到的颜色值
              //   const colorValue = match.match(/#(\w+)/)[1]
              //   const opacityValue = parseInt(colorValue.slice(-2), 16) / 255
              //   return `fill="currentColor" opacity="${opacityValue}"`
              // })

              // 提取 SVG 内容并创建 symbol
              const svgStart = data.indexOf('<svg');
              const svgEnd = data.indexOf('</svg>');
              const svgContent = data.slice(svgStart + 4, svgEnd);

              // 自动注入
              svg.insertAdjacentHTML('beforeend', `<symbol id="${src}" ${svgContent} </symbol>`);
            } catch (error) {
              console.error(`Error processing SVG data for: /tab_icon/${src}.svg`, error);
            }
          });
        })
        .catch((error) => {
          console.error(`Error loading SVG: /tab_icon/${src}.svg`, error);
        });
    }
  }, [src]);
  return (
    <svg aria-hidden="true" style={{ width, height, color: color || 'currentColor' }}>
      <use href={'#' + src} xlinkHref={'#' + src} />
    </svg>
  );
};
