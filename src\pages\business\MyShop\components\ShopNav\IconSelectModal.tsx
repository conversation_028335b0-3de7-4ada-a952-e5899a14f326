import ProSvg from '@/common/components/ProSvg';
import { ProCard } from '@ant-design/pro-components';
import { Modal, Space, Tabs, Radio, Button, Dropdown, Empty } from 'antd';
import {
  CheckOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
} from '@ant-design/icons';
import type { FC } from 'react';
import { useEffect, useState } from 'react';
import CreateIconModal from './CreateIconModal';
import { getStoreIconList, deleteStoreIcon } from '@/services/api/store';
import { message } from 'antd';

type IconInfo = {
  id?: string | number;
  name: string;
  icon: string;
  icon_active: string;
};

type IconGroup = {
  title: string;
  list: IconInfo[];
};

type IconSelectModalProps = {
  open: boolean;
  onCancel: () => void;
  onSelect: (selected: IconInfo) => void;
  iconList: IconGroup[];
  color?: string;
  onCreateCustomIcon?: () => void;
  storeId?: string | number;
};

const IconSelectModal: FC<IconSelectModalProps> = ({
  open,
  onCancel,
  onSelect,
  iconList,
  color,
  onCreateCustomIcon,
  storeId,
}) => {
  const [activeTab, setActiveTab] = useState<string>('system');
  const [iconStyle, setIconStyle] = useState<string>('通用类');
  const [selectedIcon, setSelectedIcon] = useState<IconInfo | undefined>(undefined);
  const [createModalOpen, setCreateModalOpen] = useState<boolean>(false);
  const [hoverIndex, setHoverIndex] = useState<number | null>(null);
  const [customIcons, setCustomIcons] = useState<IconInfo[]>([]);
  const [editingIcon, setEditingIcon] = useState<IconInfo | undefined>(undefined);

  const loadCustomIcons = async () => {
    if (!storeId) return;
    try {
      const res = await getStoreIconList({ storeId });
      const list = (res?.data || []).map((item: any) => ({
        id: item.id,
        name: item.name,
        icon_active: item.selectedIcon,
        icon: item.unselectedIcon,
      }));
      setCustomIcons(list);
    } catch (e) {}
  };

  // 打开弹窗或切换到自定义标签页时加载
  useEffect(() => {
    if (open && activeTab === 'custom' && storeId) {
      loadCustomIcons();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, activeTab, storeId]);

  // 弹窗关闭时重置选中状态
  useEffect(() => {
    if (!open) {
      setSelectedIcon(undefined);
    }
  }, [open]);
  const styleTitleMap: Record<string, string> = {
    通用类: '通用模版',
    乐园类: '乐园类景区',
    自然类: '自然类景区',
    文化类: '文化类景区',
  };
  const selectedGroupTitle = styleTitleMap[iconStyle] ?? iconStyle;
  const filteredIconList = iconList.filter((group) => group.title === selectedGroupTitle);
  const iconsToRender = filteredIconList[0]?.list ?? [];
  return (
    <Modal
      width={803}
      title="选择图标"
      open={open}
      onCancel={onCancel}
      footer={
        <Space>
          <Button onClick={onCancel}>取消</Button>
          <Button
            type="primary"
            onClick={() => {
              if (!selectedIcon) {
                message.warning('请先选择一个图标');
                return;
              }
              onSelect(selectedIcon);
            }}
          >
            确定
          </Button>
        </Space>
      }
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        tabBarExtraContent={
          activeTab === 'custom' ? (
            <Button
              type="link"
              icon={<PlusOutlined />}
              onClick={() => {
                if (onCreateCustomIcon) {
                  onCreateCustomIcon();
                } else {
                  setCreateModalOpen(true);
                }
              }}
            >
              新建图标
            </Button>
          ) : null
        }
        items={[
          {
            key: 'system',
            label: '系统图标',
            children: (
              <Space direction="vertical" size={24} style={{ width: '100%' }}>
                <Space align="center">
                  <span>图标风格：</span>
                  <Radio.Group value={iconStyle} onChange={(e) => setIconStyle(e.target.value)}>
                    <Radio value="通用类">通用类</Radio>
                    <Radio value="乐园类">乐园类</Radio>
                    <Radio value="自然类">自然类</Radio>
                    <Radio value="文化类">文化类</Radio>
                  </Radio.Group>
                </Space>
                <Space size={24} wrap>
                  {iconsToRender.map((listValue) => {
                    const isSelected =
                      selectedIcon?.name === listValue.name &&
                      selectedIcon?.icon === listValue.icon;
                    return (
                      <div
                        key={listValue.name}
                        onClick={() => setSelectedIcon(listValue)}
                        style={{
                          border: isSelected ? '1px solid #1890ff' : '1px solid #f0f0f0',
                          borderRadius: 8,
                          padding: 16,
                          cursor: 'pointer',
                          position: 'relative',
                          width: 180,
                          background: '#fff',
                        }}
                      >
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-around',
                            width: '100%',
                          }}
                        >
                          <ProSvg
                            src={listValue.icon_active}
                            color={color}
                            width="48px"
                            height="48px"
                          />
                          <ProSvg src={listValue.icon} color={color} width="48px" height="48px" />
                        </div>
                        <div style={{ textAlign: 'center', marginTop: 15 }}>{listValue.name}</div>
                        {isSelected && (
                          <div
                            style={{
                              position: 'absolute',
                              right: 0,
                              bottom: 0,
                              width: 20,
                              height: 20,
                              borderTopLeftRadius: 6,
                              borderBottomRightRadius: 6,
                              background: '#1890ff',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}
                          >
                            <CheckOutlined style={{ color: '#fff', fontSize: 12 }} />
                          </div>
                        )}
                      </div>
                    );
                  })}
                </Space>
              </Space>
            ),
          },
          {
            key: 'custom',
            label: '自定义图标',
            children:
              customIcons.length === 0 ? (
                <div
                  style={{
                    height: 320,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Empty description="暂无图标，快去新建吧" />
                </div>
              ) : (
                <Space size={24} wrap>
                  {customIcons.map((item, index) => {
                    const isSelected =
                      selectedIcon?.name === item.name && selectedIcon?.icon === item.icon;
                    return (
                      <div
                        key={`custom-${item.id ?? item.name}`}
                        onClick={() => setSelectedIcon(item)}
                        onMouseEnter={() => setHoverIndex(index)}
                        onMouseLeave={() => setHoverIndex(null)}
                        style={{
                          border: isSelected ? '1px solid #1890ff' : '1px solid #f0f0f0',
                          borderRadius: 8,
                          padding: 16,
                          paddingTop: 30,
                          cursor: 'pointer',
                          position: 'relative',
                          width: 180,
                          background: '#fff',
                        }}
                      >
                        {hoverIndex === index && (
                          <div
                            style={{
                              position: 'absolute',
                              top: 8,
                              right: 8,
                              zIndex: 2,
                            }}
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Dropdown
                              arrow
                              placement="bottomLeft"
                              trigger={['click']}
                              menu={{
                                items: [
                                  {
                                    key: 'edit',
                                    icon: <EditOutlined />,
                                    label: '编辑',
                                  },
                                  {
                                    key: 'delete',
                                    icon: <DeleteOutlined />,
                                    label: '删除',
                                    danger: true,
                                  },
                                ],
                                onClick: async ({ key }) => {
                                  if (key === 'edit') {
                                    setEditingIcon(item);
                                    setCreateModalOpen(true);
                                    return;
                                  }
                                  if (key === 'delete') {
                                    if (!item.id) return;
                                    Modal.confirm({
                                      title: `确定删除图标"${item.name}"？`,
                                      content: '删除后不可恢复',
                                      onOk: async () => {
                                        try {
                                          await deleteStoreIcon(item.id as any);
                                          if (selectedIcon?.id === item.id) {
                                            setSelectedIcon(undefined);
                                          }
                                          message.success('删除成功');
                                          loadCustomIcons();
                                        } catch (err) {}
                                      },
                                    });
                                  }
                                },
                              }}
                            >
                              <Button type="text" icon={<MoreOutlined />} />
                            </Dropdown>
                          </div>
                        )}
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-around',
                            width: '100%',
                          }}
                        >
                          {item.icon_active ? (
                            <img src={item.icon_active} alt="active" width={48} height={48} />
                          ) : (
                            <div style={{ width: 48, height: 48, background: '#f5f5f5' }} />
                          )}
                          {item.icon ? (
                            <img src={item.icon} alt="inactive" width={48} height={48} />
                          ) : (
                            <div style={{ width: 48, height: 48, background: '#f5f5f5' }} />
                          )}
                        </div>
                        <div style={{ textAlign: 'center', marginTop: 15 }}>{item.name}</div>
                        {isSelected && (
                          <div
                            style={{
                              position: 'absolute',
                              right: 0,
                              bottom: 0,
                              width: 20,
                              height: 20,
                              borderTopLeftRadius: 6,
                              borderBottomRightRadius: 6,
                              background: '#1890ff',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}
                          >
                            <CheckOutlined style={{ color: '#fff', fontSize: 12 }} />
                          </div>
                        )}
                      </div>
                    );
                  })}
                </Space>
              ),
          },
        ]}
      />
      <CreateIconModal
        open={createModalOpen}
        onCancel={() => {
          setEditingIcon(undefined);
          setCreateModalOpen(false);
        }}
        storeId={storeId as any}
        editing={editingIcon}
        onOk={(icon) => {
          setCreateModalOpen(false);
          setEditingIcon(undefined);
          setSelectedIcon(icon);
          loadCustomIcons();
        }}
      />
    </Modal>
  );
};

export default IconSelectModal;


